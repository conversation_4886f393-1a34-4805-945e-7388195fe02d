#ifndef CRASHTEST_H
#define CRASHTEST_H

#include <QObject>
#include <QString>

/**
 * @brief 崩溃测试类，用于验证异常处理系统
 * 
 * 该类提供多种类型的崩溃测试方法，用于验证CrashHandler的功能
 */
class CrashTest : public QObject
{
    Q_OBJECT

public:
    explicit CrashTest(QObject *parent = nullptr);

    /**
     * @brief 测试访问违例异常
     */
    Q_INVOKABLE void testAccessViolation();

    /**
     * @brief 测试除零异常
     */
    Q_INVOKABLE void testDivideByZero();

    /**
     * @brief 测试栈溢出异常
     */
    Q_INVOKABLE void testStackOverflow();

    /**
     * @brief 测试C++异常
     */
    Q_INVOKABLE void testCppException();

    /**
     * @brief 测试内存分配失败
     */
    Q_INVOKABLE void testOutOfMemory();

    /**
     * @brief 测试纯虚函数调用
     */
    Q_INVOKABLE void testPureVirtualCall();

    /**
     * @brief 测试无效参数
     */
    Q_INVOKABLE void testInvalidParameter();

    /**
     * @brief 测试手动生成转储文件
     */
    Q_INVOKABLE void testManualDump();

    /**
     * @brief 测试所有异常类型（危险！）
     */
    Q_INVOKABLE void testAllCrashes();

private:
    /**
     * @brief 递归函数用于栈溢出测试
     */
    void recursiveFunction(int depth = 0);

    /**
     * @brief 用于纯虚函数调用测试的基类
     */
    class TestBase {
    public:
        TestBase() { pureVirtualCall(); }
        virtual ~TestBase() = default;
        virtual void pureVirtualCall() = 0;
    };

    /**
     * @brief 派生类
     */
    class TestDerived : public TestBase {
    public:
        void pureVirtualCall() override {}
    };
};

#endif // CRASHTEST_H
