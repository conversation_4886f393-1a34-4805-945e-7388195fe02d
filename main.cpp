﻿#include <QGuiApplication>
#include <QApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QFontDatabase>
#include <QLoggingCategory>
#include <QProcess>
#include <QVBoxLayout>
#include <QQuickWidget>
#include <QLabel>
// #include <QtWebEngine/qtwebengineglobal.h>
#include <Qwindow>
#include <Windows.h>
#include <WinUser.h>

#include <kddockwidgets/KDDockWidgets.h>
#include <kddockwidgets/core/DockRegistry.h>
#include <kddockwidgets/core/DockWidget.h>
#include <kddockwidgets/core/MainWindow.h>
#include <kddockwidgets/qtquick/ViewFactory.h>
#include <kddockwidgets/qtquick/Platform.h>
#include <kddockwidgets/qtquick/views/DockWidget.h>
#include <kddockwidgets/qtquick/views/MainWindow.h>

#include "qmlclass/ServiceInterface.h"
#include "qmlclass/Trans.h"
#include "qmlclass/dictmanager.h"
#include "qmlclass/menumanager.h"
#include "qmlclass/toolbarmanager.h"
#include "qmlclass/steditormanager.h"
#include "qmlclass/readcodeprocess.h"
#include "qmlclass/qtpdfviewerinitializer.h"
#include "qmlclass/WebSocketTransport.h"
#include "qmlclass/wavemanage.h"

#include "IDECommon/hlog.h"
#include "IDECommon/sqlorm.h"
#include "IDEBaseControl/basecontrolversion.h"
#include "IDEProjectAndFile/projectandfileversion.h"
#include "IDEProjectAndFile/projectandfilemanage.h"
#include "IDEVariable/variableversion.h"
#include "IDEVariable/variablemanage.h"
#include "IDEDeviceAndNetwork/deviceandnetworkversion.h"
#include "IDEDeviceAndNetwork/deviceandnetworkmanage.h"
#include "IDECFCEditor/cfceditorversion.h"
#include "IDECFCEditor/cfcmanager.h"
#include "IDELDEditor/ldeditorversion.h"
#include "IDELDEditor/ldmanager.h"
#include "IDEUserAndGroup/userandgroupversion.h"
#include "IDEUserAndGroup/usergroupmanage.h"
#include "IDESTEditor/steditorversion.h"
#include "IDESOEAndTrack/soeandtrackversion.h"
#include "IDESOEAndTrack/soeandtrackmanage.h"
#include "IDEOutput/outputversion.h"
#include "IDEOutput/outputmanage.h"
#include "IDEOnlineServer/onlineserverversion.h"
#include "IDEFBDEditor/fbdeditorversion.h"
//#include "IDEFBDEditor/fbdmanager.h"
#include "IDEFBDEditor/commonmanage.h"
#include "IDEFBDEditor/sfcmanage.h"
#include "IDEFBDEditor/newldmanage.h"
#include "httpserver/httplistener.h"
#include "httpserver/httpservermanage.h"
#include "OnlineAccess/onlinedata.h"

#include "CommServ/commserv.h"
#include "OnlineAccess/targetconnect.h"
#include <QtWebView/QtWebView>

#include "ToolBox/taskstart.h"
#include "ToolBox/processmanage.h"
#include "ToolBox/newbat.h"
#include "ToolBox/toolbox.h"
#include "ToolBox/iniedit.h"
#include "./WaveRecord/waverecordinterface.h"
#include "./CompileHub/compilemanager.h"
#include "../CommServ/tcpservice.h"
#include "qmlclass/debugmanage.h"
#include "qmlclass/monitor.h"
#include "qmlclass/crashhandler.h"
#include "qmlclass/crashtest.h"
#include "../include/IDEDeviceAndNetwork/StructPLC/masterIArea.h"

#include "SimpleUI/cpp/SimpleUI.h"
int main(int argc, char *argv[])
{
    QtWebView::initialize();
    // userandgroup_test();
    // projectAndFile_test();
    //初始化日志
    Hlog::instance().InitConfig("/Settings/log_config.json");
    SqlOrm::forceDisableAllQxOrmOutput();

    // 初始化崩溃处理系统
    QString crashDumpPath = QCoreApplication::applicationDirPath() + "/crashes";
    CrashHandler::instance().initialize(crashDumpPath,
                                       CrashHandler::DumpType::MiniDumpWithDataSegs,
                                       true);

    LOG_INFO_DEFAULT("应用程序启动，崩溃处理系统已初始化，转储路径: {}", crashDumpPath.toStdString());

    // 将样式设置为Basic，不然会导致组件显示异常
    // qputenv("QT_QUICK_CONTROLS_STYLE", "Basic");
    #ifdef Q_OS_WIN
    //QGuiApplication::setAttribute(Qt::AA_UseOpenGLES);
    // 6.4及以下监听系统深色模式变化
    // qputenv("QT_QPA_PLATFORM", "windows:darkmode=2");
    #endif
    #if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QGuiApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QGuiApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    #endif
    QGuiApplication::setHighDpiScaleFactorRoundingPolicy(Qt::HighDpiScaleFactorRoundingPolicy::PassThrough);

    QGuiApplication::setOrganizationName("TGIC");
    QGuiApplication::setOrganizationDomain("https://www.ctg.com.cn");
    QGuiApplication::setApplicationName("ReliAUTO Studio");

    QGuiApplication::setAttribute(Qt::AA_ShareOpenGLContexts);
    //QtWebEngine::initialize();
    QApplication app(argc, argv);

    LOG_DEBUG_DEFAULT("Main App Start...");

    // 主应用图标设置
    app.setWindowIcon(QIcon(":/assets/img/studio.ico"));
    // 内置主题样式
    // QQuickStyle::setStyle("Universal");
    // 在main函数里，加入字体图标qml中引用即可
    // 全局字体
    if (QFontDatabase::addApplicationFont(QStringLiteral(":/assets/font/SourceHanSansCN-Regular_0.otf")) == -1)
    {
        qDebug() << "load font failed";
    }
    QFont font;
    font.setFamily("思源黑体 CN Regular");
    font.setPointSize(12);
    app.setFont(font);
    // TcpService::instance();
    KDDockWidgets::initFrontend(KDDockWidgets::FrontendType::QtQuick);

    //     // qmlRegisterType<KDDockWidgets>("com.kdab.dockwidgets",2,0,"KDDockWidgets");

    QQmlApplicationEngine engine;

    KDDockWidgets::QtQuick::Platform::instance()->setQmlEngine(&engine);

    // 加载的对象
    QQmlContext *qmlContext = engine.rootContext();

    // 注册SimpleUI
    SimpleUI::registerTypes(&engine);

    //注册对象
    //qmlRegisterType<OthereSmulationWidget>("cn.tgic", 1, 0, "OthereSmulationWidget");
    //qmlContext->setContextProperty("OthereSmulationWidget", new OthereSmulationWidget);

    // 自定义多国语言
    QString transDir = "";
    //    QString transDir       = taoQuickShowPath + "Trans/";
    if (transDir.startsWith("file:///"))
    {
        transDir = transDir.remove("file:///");
    }
    if (transDir.startsWith("qrc:/"))
    {
        transDir = transDir.remove("qrc");
    }
    // qWarning() << "transDir" << transDir;
    Trans trans;
    trans.beforeUiReady(qmlContext, transDir);

    CommServ &commserv = CommServ::instance();
    qmlContext->setContextProperty("commond", &commserv);
    commserv.serviceStart();
    QObject::connect(&app, &QApplication::aboutToQuit, &commserv, &CommServ::serviceStop);
    QObject::connect(&app, &QGuiApplication::aboutToQuit, [&]()
    {
        WaveRecordInterface::instance().stop();
        VarIOController::instance().stop();
        CompileCtrl::instance().stop();

        // 清理崩溃处理系统
        CrashHandler::instance().uninitialize();
        LOG_INFO_DEFAULT("应用程序正常退出，崩溃处理系统已清理");
    });

    // 菜单栏管理注册到QML
    MenuManager *menu = MenuManager::getInstance();
    menu->read_json();
    qmlContext->setContextProperty("menumag", menu);

    // 实时状态
    Buffer &statusBuffer = Buffer::instance();
    qmlContext->setContextProperty("statusBuffer", &statusBuffer);

    // 工具栏管理注册到QML
    ToolBarManager *toolbar = ToolBarManager::getInstance();
    toolbar->read_json();
    qmlContext->setContextProperty("toolbarmag", toolbar);

    ReadCodeProcess *codeprocess = ReadCodeProcess::getInstance();
    qmlContext->setContextProperty("codeprocess", codeprocess);

    // 字典管理
    DictManager *dict = DictManager::getInstance();
    dict->read_json();
    qmlContext->setContextProperty("dictmag", dict);

    // //     IDEV1_Common commnlib;
    // //     qDebug() << commnlib.show();

    // // 组件注册
    ServiceInterface *serviceInterface = ServiceInterface::getInstance();
    serviceInterface->appDir = QCoreApplication::applicationDirPath();
    qmlContext->setContextProperty("serviceInterface", serviceInterface);

    BaseControlVersion bControlVersion;
    qmlContext->setContextProperty("bControlVersion", &bControlVersion);

    ProjectAndFileVersion projectAndFileVersion;
    qmlContext->setContextProperty("projectAndFileVersion", &projectAndFileVersion);
    qmlContext->setContextProperty("projectAndFileManage", &ProjectAndFileManage::instance());

    VariableVersion variableVersion;
    qmlContext->setContextProperty("variableVersion", &variableVersion);
    qmlContext->setContextProperty("VariableManage", &VariableManage::instance());
    VariableManage::instance().setAppPath(QCoreApplication::applicationDirPath());

    DeviceAndNetworkVersion deviceAndNetworkVersion;
    qmlContext->setContextProperty("deviceAndNetworkVersion", &deviceAndNetworkVersion);
    qmlContext->setContextProperty("DeviceAndNetworkManage", &DeviceAndNetworkManage::instance());
    // setConfigDir
    DeviceAndNetworkManage::instance().setConfigDir("D:/clientManager/manager/data/client/12345");

    UserAndGroupVersion userAndGroupVersion;
    qmlContext->setContextProperty("userAndGroupVersion", &userAndGroupVersion);
    qmlContext->setContextProperty("UserGroupManage", &UserGroupManage::instance());

    //    STEditorVersion stEditorVersion;
    //    qmlContext->setContextProperty("stEditorVersion", &stEditorVersion);
    //    STEditorManager *stEditorManager = STEditorManager::getInstance();
    //    qmlContext->setContextProperty("STEditorManager", stEditorManager);

    //    CFCEditorVersion cFCEditorVersion;
    //    qmlContext->setContextProperty("cFCEditorVersion", &cFCEditorVersion);

    //    CFCManager &cfcManager = CFCManager::instance();
    //    cfcManager.init(QCoreApplication::applicationDirPath());
    //    qmlContext->setContextProperty("cfcManager", &cfcManager);

    LDEditorVersion ldEditorVersion;
    qmlContext->setContextProperty("ldEditorVersion", &ldEditorVersion);

    LDManager &ldManage = LDManager::instance();
    ldManage.init(QCoreApplication::applicationDirPath());
    qmlContext->setContextProperty("ldManage", &ldManage);

    SOEAndTrackVersion soeAndTrackVersion;
    qmlContext->setContextProperty("soeAndTrackVersion", &soeAndTrackVersion);
    qmlContext->setContextProperty("SoeAndTrackManage", &SoeAndTrackManage::instance());


    WaveManage &waveManage = WaveManage::instance();
    qmlContext->setContextProperty("waveManage", &waveManage);

    OutputVersion outputVersion;
    qmlContext->setContextProperty("outputVersion", &outputVersion);
    qmlContext->setContextProperty("OutputManage", &OutputManage::instance());

    OnlineServerVersion onlineServerVersion;
    qmlContext->setContextProperty("onlineServerVersion", &onlineServerVersion);

    FBDEditorVersion fbdEditorVersion;
    qmlContext->setContextProperty("fbdEditorVersion", &fbdEditorVersion);

    //FBDManager &fbdManager = FBDManager::instance();
    //fbdManager.init(QCoreApplication::applicationDirPath());
    //qmlContext->setContextProperty("fbdManager", &fbdManager);

    //新通用文件管理器
    CommonManage &commonManage = CommonManage::instance();
    commonManage.init(QCoreApplication::applicationDirPath());
    qmlContext->setContextProperty("fbdManage", &commonManage);

    //SFC编辑器
    SFCManage &sfcManage = SFCManage::instance();
    sfcManage.init(QCoreApplication::applicationDirPath());
    qmlContext->setContextProperty("sfcManage", &sfcManage);

    //监视变量管理器初始化
    DebugManage &debugManage = DebugManage::instance();
    qmlContext->setContextProperty("debugManage", &debugManage);
    debugManage.init(QCoreApplication::applicationDirPath());


    CompileManager &compileManager = CompileManager::instance();
    qmlContext->setContextProperty("compileManager", &compileManager);

    targetConn &target = targetConn::instance();
    qmlContext->setContextProperty("targetPLC", &target);
    // // const QUrl urlFirst(QStringLiteral("qrc:/first.qml"));
    // // const QUrl url(QStringLiteral("qrc:/qml/test/WebShow.qml"));

    // // 进程管理
    ProcessManage &proc = ProcessManage::instance();
    qmlContext->setContextProperty("proc", &proc);
    proc.start_process_cm();

    WaveRecordInterface &waveRecordInterface = WaveRecordInterface::instance();
    qmlContext->setContextProperty("waveRecordInterface", &waveRecordInterface);

    WatchTemp &watchtemp = WatchTemp::instance();
    qmlContext->setContextProperty("monitor", &watchtemp);

    // 注册崩溃测试类（仅在调试模式下）
#ifdef _DEBUG
    CrashTest *crashTest = new CrashTest(&app);
    qmlContext->setContextProperty("crashTest", crashTest);
    LOG_DEBUG_DEFAULT("崩溃测试类已注册到QML上下文");
#endif

    LTDev::QtPdfViewerInitializer *qtPdfViewerInitializer = LTDev::QtPdfViewerInitializer::getInstance();
    qmlContext->setContextProperty("QtPdfViewerInitializer", qtPdfViewerInitializer);

    qmlRegisterType<LTDev::WebSocketTransport>("LTDev", 1, 0, "WebSocketTransport");

    QObject::connect(&app, &QGuiApplication::aboutToQuit, LTDev::QtPdfViewerInitializer::getInstance(),
                     LTDev::QtPdfViewerInitializer::deleteInstance);

    const QUrl url(QStringLiteral("qrc:/main.qml"));
    QObject::connect(
                &engine, &QQmlApplicationEngine::objectCreated,
                &app, [url](QObject * obj, const QUrl & objUrl)
    {
        if (!obj && url == objUrl)
        {
            QCoreApplication::exit(-1);
        }
    }, Qt::QueuedConnection);
    engine.load(url);

    //    qDebug() << "area count------------" << KDDockWidgets::DockRegistry::self()->mainDockingAreas().count();
    //    auto mainArea = KDDockWidgets::DockRegistry::self()->mainDockingAreas().constFirst();
    //    for (auto &dock : KDDockWidgets::DockRegistry::self()->dockwidgets())
    //    {
    //        if (dock->uniqueName() == "mainDock")
    //        {
    //            auto mainWindowDockWidget =
    //                        new KDDockWidgets::QtQuick::DockWidget(QStringLiteral("MyMainWindow-2-DW"));


    //            mainArea->addDockWidget(mainWindowDockWidget, KDDockWidgets::Location_OnTop);
    //        }
    //    }

    //    QProcess *m_process = new QProcess(THIS_);

    //    m_process->start("./ParamBackupTool.exe");
    //    m_process->waitForStarted();
    //    //m_process->waitForFinished(10);

    //    //启动的第三方应用联动关闭
    //    QObject::connect(&app, &QApplication::aboutToQuit, [m_process]()
    //    {
    //        m_process->kill();
    //        m_process->waitForFinished();
    //    });


    //    QString wclassname = "Qt5152QWindowOwnDCIcon";//窗口的类名
    //    QString windowtitlename = "Modbus-TCP参数备份软件 V1.0";//窗口标题名
    //    while (true)
    //    {
    //        HWND hwnd = FindWindowW((LPCWSTR)wclassname.unicode(),
    //                                (LPCWSTR)windowtitlename.unicode());
    //        if (hwnd != NULL)
    //        {
    //            //qDebug() << "The operation took" << timer.elapsed() << "milliseconds";

    //            qDebug() << "hwnd" << hwnd;
    //            WId m_wid = (WId)hwnd;
    //            qDebug() << "m_wid" << m_wid;
    //            QWindow *M_WINDOW = QWindow::fromWinId(m_wid);
    //            M_WINDOW->setFlags(M_WINDOW->flags() | Qt::CustomizeWindowHint | Qt::WindowTitleHint);
    //            QWidget *container = QWidget::createWindowContainer(M_WINDOW); //创建新的widget

    //            QQuickWidget *qmlWidget = new QQuickWidget(QUrl("qrc:/qml/EmptyCom.qml"));
    //            qmlWidget->setResizeMode(QQuickWidget::SizeRootObjectToView);
    //            qmlWidget->resize(1024, 768);
    //            container->setParent(qmlWidget);
    //            container->resize(qmlWidget->width(), qmlWidget->height());
    //            //            QObject::connect(qmlWidget,&QQuickWidget::,[container](){
    //            //                container->resize(qmlWidget->width(), qmlWidget->height());
    //            //            });
    //            qmlWidget->show();

    //            break;
    //        }
    //        Sleep(50);

    //    }


    //VarIOController::instance();

    // 启动webeditor
    // QString currentPath = QGuiApplication::applicationDirPath();
    // HttpServerManage::instance().createHttpServer("localhost", 8080,currentPath + QDir::separator() + "Settings" + QDir::separator() + "httpConfig.ini");
    qDebug() << "[INFO] configbin size" << sizeof(MASTER_I_AREA_INIT_CFG_ST);
    return app.exec();
}
