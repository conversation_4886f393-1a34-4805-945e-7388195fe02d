#include "crashtest.h"
#include "crashhandler.h"
#include "IDECommon/hlog.h"
#include <QDebug>
#include <stdexcept>
#include <new>

#ifdef Q_OS_WIN
#include <Windows.h>
#endif

CrashTest::CrashTest(QObject *parent)
    : QObject(parent)
{
}

void CrashTest::testAccessViolation()
{
    qWarning() << "CrashTest: 即将测试访问违例异常...";
    LOG_WARN_DEFAULT("CrashTest: 测试访问违例异常");
    
    // 故意访问空指针
    int* nullPtr = nullptr;
    *nullPtr = 42; // 这将导致访问违例
}

void CrashTest::testDivideByZero()
{
    qWarning() << "CrashTest: 即将测试除零异常...";
    LOG_WARN_DEFAULT("CrashTest: 测试除零异常");
    
    // 整数除零
    int a = 10;
    int b = 0;
    int result = a / b; // 这将导致除零异常
    Q_UNUSED(result)
}

void CrashTest::testStackOverflow()
{
    qWarning() << "CrashTest: 即将测试栈溢出异常...";
    LOG_WARN_DEFAULT("CrashTest: 测试栈溢出异常");
    
    // 无限递归导致栈溢出
    recursiveFunction();
}

void CrashTest::testCppException()
{
    qWarning() << "CrashTest: 即将测试C++异常...";
    LOG_WARN_DEFAULT("CrashTest: 测试C++异常");
    
    // 抛出未捕获的异常
    throw std::runtime_error("测试用的C++异常");
}

void CrashTest::testOutOfMemory()
{
    qWarning() << "CrashTest: 即将测试内存分配失败...";
    LOG_WARN_DEFAULT("CrashTest: 测试内存分配失败");
    
    try {
        // 尝试分配大量内存
        while (true) {
            char* ptr = new char[1024 * 1024 * 1024]; // 1GB
            memset(ptr, 0, 1024 * 1024 * 1024);
        }
    } catch (const std::bad_alloc& e) {
        qDebug() << "内存分配失败被捕获:" << e.what();
        throw; // 重新抛出以触发异常处理器
    }
}

void CrashTest::testPureVirtualCall()
{
    qWarning() << "CrashTest: 即将测试纯虚函数调用...";
    LOG_WARN_DEFAULT("CrashTest: 测试纯虚函数调用");
    
    // 在构造函数中调用纯虚函数
    TestDerived derived; // 这将在构造函数中调用纯虚函数
}

void CrashTest::testInvalidParameter()
{
    qWarning() << "CrashTest: 即将测试无效参数...";
    LOG_WARN_DEFAULT("CrashTest: 测试无效参数");
    
#ifdef Q_OS_WIN
    // 使用Windows CRT函数传递无效参数
    FILE* file = nullptr;
    fclose(file); // 传递空指针给fclose
#else
    // 非Windows平台的替代测试
    throw std::invalid_argument("测试无效参数异常");
#endif
}

void CrashTest::testManualDump()
{
    qWarning() << "CrashTest: 测试手动生成转储文件...";
    LOG_WARN_DEFAULT("CrashTest: 测试手动生成转储文件");
    
    bool success = CrashHandler::instance().generateDump("手动测试转储");
    if (success) {
        qDebug() << "手动转储文件生成成功";
        LOG_INFO_DEFAULT("手动转储文件生成成功");
    } else {
        qWarning() << "手动转储文件生成失败";
        LOG_WARN_DEFAULT("手动转储文件生成失败");
    }
}

void CrashTest::testAllCrashes()
{
    qCritical() << "CrashTest: 警告！即将测试所有崩溃类型，这将导致程序终止！";
    LOG_CRITI_DEFAULT("CrashTest: 警告！即将测试所有崩溃类型");
    
    // 首先测试手动转储
    testManualDump();
    
    // 等待一秒
    QThread::msleep(1000);
    
    // 然后测试真正的崩溃（选择一个相对安全的）
    testAccessViolation();
}

void CrashTest::recursiveFunction(int depth)
{
    // 创建大量局部变量消耗栈空间
    char buffer[1024];
    memset(buffer, depth % 256, sizeof(buffer));
    
    // 无限递归
    recursiveFunction(depth + 1);
    
    // 防止编译器优化
    volatile char* ptr = buffer;
    Q_UNUSED(ptr)
}
