#ifndef CRASHHANDLER_H
#define CRASHHANDLER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QDir>
#include <QDebug>
#include <QCoreApplication>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>

#ifdef Q_OS_WIN
#include <Windows.h>
#include <DbgHelp.h>
#include <signal.h>
#include <exception>
#include <new>
#endif

#include "IDECommon/hlog.h"

/**
 * @brief Windows SEH异常处理和内存转储系统
 * 
 * 该类提供全面的异常捕获机制，包括：
 * - Windows结构化异常处理 (SEH)
 * - POSIX信号处理
 * - C++异常处理
 * - 内存转储文件生成
 * - 崩溃日志记录
 */
class CrashHandler : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 内存转储类型枚举
     */
    enum class DumpType {
        MiniDump = 0,           // 最小转储，包含基本信息
        MiniDumpWithDataSegs,   // 包含数据段的小转储
        MiniDumpWithFullMemory, // 完整内存转储
        MiniDumpWithHandleData, // 包含句柄数据的转储
        MiniDumpFilterMemory,   // 过滤内存转储
        MiniDumpScanMemory      // 扫描内存转储
    };

    /**
     * @brief 异常类型枚举
     */
    enum class ExceptionType {
        Unknown = 0,
        AccessViolation,        // 访问违例
        StackOverflow,          // 栈溢出
        IllegalInstruction,     // 非法指令
        DivideByZero,          // 除零错误
        FloatingPointError,     // 浮点错误
        BreakPoint,            // 断点异常
        Signal,                // 信号异常
        CppException,          // C++异常
        OutOfMemory            // 内存不足
    };

    /**
     * @brief 崩溃信息结构体
     */
    struct CrashInfo {
        ExceptionType type;
        DWORD exceptionCode;
        QString exceptionMessage;
        QString dumpFilePath;
        QString logMessage;
        QDateTime crashTime;
        DWORD processId;
        DWORD threadId;
        QString moduleName;
        QString functionName;
        quint64 exceptionAddress;
        QString stackTrace;
    };

private:
    explicit CrashHandler(QObject *parent = nullptr);

public:
    ~CrashHandler();
    
    /**
     * @brief 获取单例实例
     */
    static CrashHandler& instance();

    /**
     * @brief 初始化异常处理系统
     * @param dumpPath 转储文件保存路径
     * @param dumpType 转储类型
     * @param enableLogging 是否启用日志记录
     * @return 初始化是否成功
     */
    bool initialize(const QString& dumpPath = QString(), 
                   DumpType dumpType = DumpType::MiniDumpWithDataSegs,
                   bool enableLogging = true);

    /**
     * @brief 反初始化异常处理系统
     */
    void uninitialize();

    /**
     * @brief 设置转储文件保存路径
     */
    void setDumpPath(const QString& path);

    /**
     * @brief 获取转储文件保存路径
     */
    QString getDumpPath() const;

    /**
     * @brief 设置转储类型
     */
    void setDumpType(DumpType type);

    /**
     * @brief 获取转储类型
     */
    DumpType getDumpType() const;

    /**
     * @brief 启用/禁用日志记录
     */
    void setLoggingEnabled(bool enabled);

    /**
     * @brief 检查日志记录是否启用
     */
    bool isLoggingEnabled() const;

    /**
     * @brief 手动生成转储文件（用于测试）
     */
    bool generateDump(const QString& reason = "Manual dump");

    /**
     * @brief 获取最后一次崩溃信息
     */
    CrashInfo getLastCrashInfo() const;

signals:
    /**
     * @brief 崩溃发生时发出的信号
     */
    void crashOccurred(const CrashInfo& crashInfo);

private slots:
    /**
     * @brief 处理崩溃信息记录
     */
    void handleCrashInfo(const CrashInfo& crashInfo);

private:
    /**
     * @brief Windows SEH异常处理函数
     */
    static LONG WINAPI unhandledExceptionFilter(EXCEPTION_POINTERS* exceptionInfo);

    /**
     * @brief 信号处理函数
     */
    static void signalHandler(int signal);

    /**
     * @brief C++异常处理函数
     */
    static void terminateHandler();

    /**
     * @brief new操作符失败处理函数
     */
    static void newHandler();

    /**
     * @brief 纯虚函数调用处理函数
     */
    static void purecallHandler();

    /**
     * @brief 无效参数处理函数
     */
    static void invalidParameterHandler(const wchar_t* expression,
                                      const wchar_t* function,
                                      const wchar_t* file,
                                      unsigned int line,
                                      uintptr_t pReserved);

    /**
     * @brief 生成内存转储文件
     */
    static bool createDumpFile(EXCEPTION_POINTERS* exceptionInfo, 
                              const QString& dumpPath,
                              DumpType dumpType);

    /**
     * @brief 记录崩溃日志
     */
    static void logCrashInfo(const CrashInfo& crashInfo);

    /**
     * @brief 解析异常信息
     */
    static CrashInfo parseExceptionInfo(EXCEPTION_POINTERS* exceptionInfo);

    /**
     * @brief 获取异常类型字符串
     */
    static QString getExceptionTypeString(ExceptionType type);

    /**
     * @brief 获取异常代码字符串
     */
    static QString getExceptionCodeString(DWORD code);

    /**
     * @brief 生成转储文件名
     */
    static QString generateDumpFileName();

    /**
     * @brief 获取模块信息
     */
    static QString getModuleInfo(HMODULE hModule);

    /**
     * @brief 获取函数名称
     */
    static QString getFunctionName(DWORD64 address);

    /**
     * @brief 生成堆栈跟踪
     */
    static QString generateStackTrace(CONTEXT* context);

private:
    static CrashHandler* s_instance;
    static QMutex s_mutex;
    static QString s_dumpPath;
    static DumpType s_dumpType;
    static bool s_loggingEnabled;
    static bool s_initialized;
    static CrashInfo s_lastCrashInfo;

    // 原始处理器保存
    static LPTOP_LEVEL_EXCEPTION_FILTER s_previousFilter;
    static void (*s_previousTerminateHandler)();
    static void (*s_previousNewHandler)();
    static _purecall_handler s_previousPurecallHandler;
    static _invalid_parameter_handler s_previousInvalidParameterHandler;
};

#endif // CRASHHANDLER_H
