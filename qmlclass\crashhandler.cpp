#include "crashhandler.h"
#include <QStandardPaths>
#include <QFileInfo>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>

#ifdef Q_OS_WIN
#pragma comment(lib, "dbghelp.lib")
#endif

// 静态成员变量定义
CrashHandler* CrashHandler::s_instance = nullptr;
QMutex CrashHandler::s_mutex;
QString CrashHandler::s_dumpPath;
CrashHandler::DumpType CrashHandler::s_dumpType = CrashHandler::DumpType::MiniDumpWithDataSegs;
bool CrashHandler::s_loggingEnabled = true;
bool CrashHandler::s_initialized = false;
CrashHandler::CrashInfo CrashHandler::s_lastCrashInfo;

// 原始处理器保存
LPTOP_LEVEL_EXCEPTION_FILTER CrashHandler::s_previousFilter = nullptr;
void (*CrashHandler::s_previousTerminateHandler)() = nullptr;
void (*CrashHandler::s_previousNewHandler)() = nullptr;
_purecall_handler CrashHandler::s_previousPurecallHandler = nullptr;
_invalid_parameter_handler CrashHandler::s_previousInvalidParameterHandler = nullptr;

CrashHandler::CrashHandler(QObject *parent)
    : QObject(parent)
{
    // 连接信号槽用于线程安全的日志记录
    connect(this, &CrashHandler::crashOccurred, 
            this, &CrashHandler::handleCrashInfo, 
            Qt::QueuedConnection);
}

CrashHandler::~CrashHandler()
{
    uninitialize();
}

CrashHandler& CrashHandler::instance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new CrashHandler();
    }
    return *s_instance;
}

bool CrashHandler::initialize(const QString& dumpPath, DumpType dumpType, bool enableLogging)
{
    QMutexLocker locker(&s_mutex);
    
    if (s_initialized) {
        qDebug() << "CrashHandler: 已经初始化";
        return true;
    }

    // 设置转储路径
    if (dumpPath.isEmpty()) {
        s_dumpPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/crashes";
    } else {
        s_dumpPath = dumpPath;
    }

    // 确保转储目录存在
    QDir dir(s_dumpPath);
    if (!dir.exists()) {
        if (!dir.mkpath(s_dumpPath)) {
            qCritical() << "CrashHandler: 无法创建转储目录:" << s_dumpPath;
            return false;
        }
    }

    s_dumpType = dumpType;
    s_loggingEnabled = enableLogging;

#ifdef Q_OS_WIN
    // 注册Windows SEH异常处理器
    s_previousFilter = SetUnhandledExceptionFilter(unhandledExceptionFilter);
    
    // 注册C++异常处理器
    s_previousTerminateHandler = std::set_terminate(terminateHandler);
    s_previousNewHandler = std::set_new_handler(newHandler);
    
    // 注册CRT错误处理器
    s_previousPurecallHandler = _set_purecall_handler(purecallHandler);
    s_previousInvalidParameterHandler = _set_invalid_parameter_handler(invalidParameterHandler);
    
    // 注册信号处理器
    signal(SIGABRT, signalHandler);
    signal(SIGFPE, signalHandler);
    signal(SIGILL, signalHandler);
    signal(SIGINT, signalHandler);
    signal(SIGSEGV, signalHandler);
    signal(SIGTERM, signalHandler);
#endif

    s_initialized = true;
    
    if (s_loggingEnabled) {
        LOG_INFO_DEFAULT("CrashHandler: 异常处理系统初始化成功，转储路径: {}", s_dumpPath.toStdString());
    }
    
    return true;
}

void CrashHandler::uninitialize()
{
    QMutexLocker locker(&s_mutex);
    
    if (!s_initialized) {
        return;
    }

#ifdef Q_OS_WIN
    // 恢复原始处理器
    if (s_previousFilter) {
        SetUnhandledExceptionFilter(s_previousFilter);
        s_previousFilter = nullptr;
    }
    
    if (s_previousTerminateHandler) {
        std::set_terminate(s_previousTerminateHandler);
        s_previousTerminateHandler = nullptr;
    }
    
    if (s_previousNewHandler) {
        std::set_new_handler(s_previousNewHandler);
        s_previousNewHandler = nullptr;
    }
    
    if (s_previousPurecallHandler) {
        _set_purecall_handler(s_previousPurecallHandler);
        s_previousPurecallHandler = nullptr;
    }
    
    if (s_previousInvalidParameterHandler) {
        _set_invalid_parameter_handler(s_previousInvalidParameterHandler);
        s_previousInvalidParameterHandler = nullptr;
    }
    
    // 恢复默认信号处理器
    signal(SIGABRT, SIG_DFL);
    signal(SIGFPE, SIG_DFL);
    signal(SIGILL, SIG_DFL);
    signal(SIGINT, SIG_DFL);
    signal(SIGSEGV, SIG_DFL);
    signal(SIGTERM, SIG_DFL);
#endif

    s_initialized = false;
    
    if (s_loggingEnabled) {
        LOG_INFO_DEFAULT("CrashHandler: 异常处理系统已反初始化");
    }
}

void CrashHandler::setDumpPath(const QString& path)
{
    QMutexLocker locker(&s_mutex);
    s_dumpPath = path;
    
    // 确保目录存在
    QDir dir(s_dumpPath);
    if (!dir.exists()) {
        dir.mkpath(s_dumpPath);
    }
}

QString CrashHandler::getDumpPath() const
{
    QMutexLocker locker(&s_mutex);
    return s_dumpPath;
}

void CrashHandler::setDumpType(DumpType type)
{
    QMutexLocker locker(&s_mutex);
    s_dumpType = type;
}

CrashHandler::DumpType CrashHandler::getDumpType() const
{
    QMutexLocker locker(&s_mutex);
    return s_dumpType;
}

void CrashHandler::setLoggingEnabled(bool enabled)
{
    QMutexLocker locker(&s_mutex);
    s_loggingEnabled = enabled;
}

bool CrashHandler::isLoggingEnabled() const
{
    QMutexLocker locker(&s_mutex);
    return s_loggingEnabled;
}

bool CrashHandler::generateDump(const QString& reason)
{
#ifdef Q_OS_WIN
    QString dumpFileName = generateDumpFileName();
    QString fullPath = s_dumpPath + "/" + dumpFileName;
    
    HANDLE hFile = CreateFileA(fullPath.toLocal8Bit().constData(),
                              GENERIC_WRITE,
                              0,
                              nullptr,
                              CREATE_ALWAYS,
                              FILE_ATTRIBUTE_NORMAL,
                              nullptr);
    
    if (hFile == INVALID_HANDLE_VALUE) {
        if (s_loggingEnabled) {
            LOG_ERROR_DEFAULT("CrashHandler: 无法创建转储文件: {}", fullPath.toStdString());
        }
        return false;
    }
    
    MINIDUMP_TYPE dumpType = MiniDumpNormal;
    switch (s_dumpType) {
        case DumpType::MiniDump:
            dumpType = MiniDumpNormal;
            break;
        case DumpType::MiniDumpWithDataSegs:
            dumpType = static_cast<MINIDUMP_TYPE>(MiniDumpWithDataSegs | MiniDumpWithHandleData);
            break;
        case DumpType::MiniDumpWithFullMemory:
            dumpType = MiniDumpWithFullMemory;
            break;
        case DumpType::MiniDumpWithHandleData:
            dumpType = MiniDumpWithHandleData;
            break;
        case DumpType::MiniDumpFilterMemory:
            dumpType = MiniDumpFilterMemory;
            break;
        case DumpType::MiniDumpScanMemory:
            dumpType = MiniDumpScanMemory;
            break;
    }
    
    BOOL result = MiniDumpWriteDump(GetCurrentProcess(),
                                   GetCurrentProcessId(),
                                   hFile,
                                   dumpType,
                                   nullptr,
                                   nullptr,
                                   nullptr);
    
    CloseHandle(hFile);
    
    if (result && s_loggingEnabled) {
        LOG_INFO_DEFAULT("CrashHandler: 手动转储文件生成成功: {} (原因: {})", 
                        fullPath.toStdString(), reason.toStdString());
    }
    
    return result != FALSE;
#else
    Q_UNUSED(reason)
    return false;
#endif
}

CrashHandler::CrashInfo CrashHandler::getLastCrashInfo() const
{
    QMutexLocker locker(&s_mutex);
    return s_lastCrashInfo;
}

void CrashHandler::handleCrashInfo(const CrashInfo& crashInfo)
{
    // 在主线程中安全地记录崩溃信息
    logCrashInfo(crashInfo);
}

#ifdef Q_OS_WIN
LONG WINAPI CrashHandler::unhandledExceptionFilter(EXCEPTION_POINTERS* exceptionInfo)
{
    // 解析异常信息
    CrashInfo crashInfo = parseExceptionInfo(exceptionInfo);

    // 生成转储文件
    QString dumpFileName = generateDumpFileName();
    crashInfo.dumpFilePath = s_dumpPath + "/" + dumpFileName;

    bool dumpSuccess = createDumpFile(exceptionInfo, crashInfo.dumpFilePath, s_dumpType);

    if (s_loggingEnabled) {
        if (dumpSuccess) {
            LOG_CRITI_DEFAULT("CrashHandler: 未处理异常发生，转储文件已生成: {}",
                             crashInfo.dumpFilePath.toStdString());
        } else {
            LOG_CRITI_DEFAULT("CrashHandler: 未处理异常发生，转储文件生成失败");
        }

        LOG_CRITI_DEFAULT("异常类型: {} (0x{:X})",
                         getExceptionCodeString(crashInfo.exceptionCode).toStdString(),
                         crashInfo.exceptionCode);
        LOG_CRITI_DEFAULT("异常地址: 0x{:X}", crashInfo.exceptionAddress);
        LOG_CRITI_DEFAULT("进程ID: {}, 线程ID: {}", crashInfo.processId, crashInfo.threadId);
    }

    // 保存最后崩溃信息
    s_lastCrashInfo = crashInfo;

    // 发出信号（如果实例存在）
    if (s_instance) {
        emit s_instance->crashOccurred(crashInfo);
    }

    // 返回EXCEPTION_EXECUTE_HANDLER让系统终止进程
    return EXCEPTION_EXECUTE_HANDLER;
}

void CrashHandler::signalHandler(int signal)
{
    CrashInfo crashInfo;
    crashInfo.type = ExceptionType::Signal;
    crashInfo.exceptionCode = signal;
    crashInfo.crashTime = QDateTime::currentDateTime();
    crashInfo.processId = GetCurrentProcessId();
    crashInfo.threadId = GetCurrentThreadId();

    switch (signal) {
        case SIGABRT:
            crashInfo.exceptionMessage = "程序异常终止 (SIGABRT)";
            break;
        case SIGFPE:
            crashInfo.exceptionMessage = "浮点异常 (SIGFPE)";
            break;
        case SIGILL:
            crashInfo.exceptionMessage = "非法指令 (SIGILL)";
            break;
        case SIGINT:
            crashInfo.exceptionMessage = "中断信号 (SIGINT)";
            break;
        case SIGSEGV:
            crashInfo.exceptionMessage = "段错误 (SIGSEGV)";
            break;
        case SIGTERM:
            crashInfo.exceptionMessage = "终止信号 (SIGTERM)";
            break;
        default:
            crashInfo.exceptionMessage = QString("未知信号 (%1)").arg(signal);
            break;
    }

    // 生成转储文件
    QString dumpFileName = generateDumpFileName();
    crashInfo.dumpFilePath = s_dumpPath + "/" + dumpFileName;

    // 手动生成转储
    HANDLE hFile = CreateFileA(crashInfo.dumpFilePath.toLocal8Bit().constData(),
                              GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS,
                              FILE_ATTRIBUTE_NORMAL, nullptr);

    if (hFile != INVALID_HANDLE_VALUE) {
        MINIDUMP_TYPE dumpType = MiniDumpNormal;
        switch (s_dumpType) {
            case DumpType::MiniDumpWithDataSegs:
                dumpType = static_cast<MINIDUMP_TYPE>(MiniDumpWithDataSegs | MiniDumpWithHandleData);
                break;
            case DumpType::MiniDumpWithFullMemory:
                dumpType = MiniDumpWithFullMemory;
                break;
            default:
                break;
        }

        MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), hFile,
                         dumpType, nullptr, nullptr, nullptr);
        CloseHandle(hFile);
    }

    if (s_loggingEnabled) {
        LOG_CRITI_DEFAULT("CrashHandler: 信号异常 - {}", crashInfo.exceptionMessage.toStdString());
        LOG_CRITI_DEFAULT("转储文件: {}", crashInfo.dumpFilePath.toStdString());
    }

    s_lastCrashInfo = crashInfo;

    if (s_instance) {
        emit s_instance->crashOccurred(crashInfo);
    }

    // 恢复默认处理器并重新发送信号
    signal(signal, SIG_DFL);
    raise(signal);
}

void CrashHandler::terminateHandler()
{
    CrashInfo crashInfo;
    crashInfo.type = ExceptionType::CppException;
    crashInfo.exceptionMessage = "未捕获的C++异常导致程序终止";
    crashInfo.crashTime = QDateTime::currentDateTime();
    crashInfo.processId = GetCurrentProcessId();
    crashInfo.threadId = GetCurrentThreadId();

    // 尝试获取当前异常信息
    try {
        std::rethrow_exception(std::current_exception());
    } catch (const std::exception& e) {
        crashInfo.exceptionMessage = QString("C++异常: %1").arg(e.what());
    } catch (...) {
        crashInfo.exceptionMessage = "未知C++异常";
    }

    // 生成转储文件
    QString dumpFileName = generateDumpFileName();
    crashInfo.dumpFilePath = s_dumpPath + "/" + dumpFileName;

    HANDLE hFile = CreateFileA(crashInfo.dumpFilePath.toLocal8Bit().constData(),
                              GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS,
                              FILE_ATTRIBUTE_NORMAL, nullptr);

    if (hFile != INVALID_HANDLE_VALUE) {
        MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), hFile,
                         MiniDumpNormal, nullptr, nullptr, nullptr);
        CloseHandle(hFile);
    }

    if (s_loggingEnabled) {
        LOG_CRITI_DEFAULT("CrashHandler: C++异常终止 - {}", crashInfo.exceptionMessage.toStdString());
        LOG_CRITI_DEFAULT("转储文件: {}", crashInfo.dumpFilePath.toStdString());
    }

    s_lastCrashInfo = crashInfo;

    if (s_instance) {
        emit s_instance->crashOccurred(crashInfo);
    }

    // 调用原始处理器或终止程序
    if (s_previousTerminateHandler) {
        s_previousTerminateHandler();
    } else {
        std::abort();
    }
}

void CrashHandler::newHandler()
{
    CrashInfo crashInfo;
    crashInfo.type = ExceptionType::OutOfMemory;
    crashInfo.exceptionMessage = "内存分配失败 (new操作符)";
    crashInfo.crashTime = QDateTime::currentDateTime();
    crashInfo.processId = GetCurrentProcessId();
    crashInfo.threadId = GetCurrentThreadId();

    if (s_loggingEnabled) {
        LOG_CRITI_DEFAULT("CrashHandler: 内存分配失败");
    }

    s_lastCrashInfo = crashInfo;

    if (s_instance) {
        emit s_instance->crashOccurred(crashInfo);
    }

    // 抛出bad_alloc异常
    throw std::bad_alloc();
}

void CrashHandler::purecallHandler()
{
    CrashInfo crashInfo;
    crashInfo.type = ExceptionType::CppException;
    crashInfo.exceptionMessage = "纯虚函数调用错误";
    crashInfo.crashTime = QDateTime::currentDateTime();
    crashInfo.processId = GetCurrentProcessId();
    crashInfo.threadId = GetCurrentThreadId();

    if (s_loggingEnabled) {
        LOG_CRITI_DEFAULT("CrashHandler: 纯虚函数调用错误");
    }

    s_lastCrashInfo = crashInfo;

    if (s_instance) {
        emit s_instance->crashOccurred(crashInfo);
    }

    std::abort();
}

void CrashHandler::invalidParameterHandler(const wchar_t* expression,
                                          const wchar_t* function,
                                          const wchar_t* file,
                                          unsigned int line,
                                          uintptr_t pReserved)
{
    Q_UNUSED(pReserved)

    CrashInfo crashInfo;
    crashInfo.type = ExceptionType::CppException;
    crashInfo.crashTime = QDateTime::currentDateTime();
    crashInfo.processId = GetCurrentProcessId();
    crashInfo.threadId = GetCurrentThreadId();

    QString expr = expression ? QString::fromWCharArray(expression) : "未知";
    QString func = function ? QString::fromWCharArray(function) : "未知";
    QString fileName = file ? QString::fromWCharArray(file) : "未知";

    crashInfo.exceptionMessage = QString("无效参数错误 - 表达式: %1, 函数: %2, 文件: %3:%4")
                                 .arg(expr, func, fileName).arg(line);

    if (s_loggingEnabled) {
        LOG_CRITI_DEFAULT("CrashHandler: 无效参数错误 - {}", crashInfo.exceptionMessage.toStdString());
    }

    s_lastCrashInfo = crashInfo;

    if (s_instance) {
        emit s_instance->crashOccurred(crashInfo);
    }

    std::abort();
}

bool CrashHandler::createDumpFile(EXCEPTION_POINTERS* exceptionInfo,
                                 const QString& dumpPath,
                                 DumpType dumpType)
{
    HANDLE hFile = CreateFileA(dumpPath.toLocal8Bit().constData(),
                              GENERIC_WRITE,
                              0,
                              nullptr,
                              CREATE_ALWAYS,
                              FILE_ATTRIBUTE_NORMAL,
                              nullptr);

    if (hFile == INVALID_HANDLE_VALUE) {
        return false;
    }

    MINIDUMP_TYPE miniDumpType = MiniDumpNormal;
    switch (dumpType) {
        case DumpType::MiniDump:
            miniDumpType = MiniDumpNormal;
            break;
        case DumpType::MiniDumpWithDataSegs:
            miniDumpType = static_cast<MINIDUMP_TYPE>(MiniDumpWithDataSegs | MiniDumpWithHandleData | MiniDumpWithThreadInfo);
            break;
        case DumpType::MiniDumpWithFullMemory:
            miniDumpType = MiniDumpWithFullMemory;
            break;
        case DumpType::MiniDumpWithHandleData:
            miniDumpType = MiniDumpWithHandleData;
            break;
        case DumpType::MiniDumpFilterMemory:
            miniDumpType = MiniDumpFilterMemory;
            break;
        case DumpType::MiniDumpScanMemory:
            miniDumpType = MiniDumpScanMemory;
            break;
    }

    MINIDUMP_EXCEPTION_INFORMATION exceptionParam;
    exceptionParam.ThreadId = GetCurrentThreadId();
    exceptionParam.ExceptionPointers = exceptionInfo;
    exceptionParam.ClientPointers = FALSE;

    BOOL result = MiniDumpWriteDump(GetCurrentProcess(),
                                   GetCurrentProcessId(),
                                   hFile,
                                   miniDumpType,
                                   exceptionInfo ? &exceptionParam : nullptr,
                                   nullptr,
                                   nullptr);

    CloseHandle(hFile);
    return result != FALSE;
}

void CrashHandler::logCrashInfo(const CrashInfo& crashInfo)
{
    if (!s_loggingEnabled) {
        return;
    }

    // 记录详细的崩溃信息
    LOG_CRITI_DEFAULT("=== 程序崩溃报告 ===");
    LOG_CRITI_DEFAULT("崩溃时间: {}", crashInfo.crashTime.toString("yyyy-MM-dd hh:mm:ss").toStdString());
    LOG_CRITI_DEFAULT("异常类型: {}", getExceptionTypeString(crashInfo.type).toStdString());
    LOG_CRITI_DEFAULT("异常代码: 0x{:X}", crashInfo.exceptionCode);
    LOG_CRITI_DEFAULT("异常消息: {}", crashInfo.exceptionMessage.toStdString());
    LOG_CRITI_DEFAULT("异常地址: 0x{:X}", crashInfo.exceptionAddress);
    LOG_CRITI_DEFAULT("进程ID: {}", crashInfo.processId);
    LOG_CRITI_DEFAULT("线程ID: {}", crashInfo.threadId);

    if (!crashInfo.moduleName.isEmpty()) {
        LOG_CRITI_DEFAULT("模块名称: {}", crashInfo.moduleName.toStdString());
    }

    if (!crashInfo.functionName.isEmpty()) {
        LOG_CRITI_DEFAULT("函数名称: {}", crashInfo.functionName.toStdString());
    }

    if (!crashInfo.dumpFilePath.isEmpty()) {
        LOG_CRITI_DEFAULT("转储文件: {}", crashInfo.dumpFilePath.toStdString());
    }

    if (!crashInfo.stackTrace.isEmpty()) {
        LOG_CRITI_DEFAULT("堆栈跟踪:\n{}", crashInfo.stackTrace.toStdString());
    }

    LOG_CRITI_DEFAULT("=== 崩溃报告结束 ===");

    // 强制刷新日志
    if (auto logger = Hlog::instance().writeLog()) {
        logger->flush();
    }
}

CrashHandler::CrashInfo CrashHandler::parseExceptionInfo(EXCEPTION_POINTERS* exceptionInfo)
{
    CrashInfo crashInfo;
    crashInfo.crashTime = QDateTime::currentDateTime();
    crashInfo.processId = GetCurrentProcessId();
    crashInfo.threadId = GetCurrentThreadId();

    if (!exceptionInfo || !exceptionInfo->ExceptionRecord) {
        crashInfo.type = ExceptionType::Unknown;
        crashInfo.exceptionMessage = "未知异常";
        return crashInfo;
    }

    EXCEPTION_RECORD* record = exceptionInfo->ExceptionRecord;
    crashInfo.exceptionCode = record->ExceptionCode;
    crashInfo.exceptionAddress = reinterpret_cast<quint64>(record->ExceptionAddress);

    // 根据异常代码确定异常类型
    switch (record->ExceptionCode) {
        case EXCEPTION_ACCESS_VIOLATION:
            crashInfo.type = ExceptionType::AccessViolation;
            if (record->NumberParameters >= 2) {
                ULONG_PTR operation = record->ExceptionInformation[0];
                ULONG_PTR address = record->ExceptionInformation[1];
                crashInfo.exceptionMessage = QString("访问违例 - %1地址 0x%2")
                    .arg(operation == 0 ? "读取" : (operation == 1 ? "写入" : "执行"))
                    .arg(address, 0, 16);
            } else {
                crashInfo.exceptionMessage = "访问违例";
            }
            break;

        case EXCEPTION_STACK_OVERFLOW:
            crashInfo.type = ExceptionType::StackOverflow;
            crashInfo.exceptionMessage = "栈溢出";
            break;

        case EXCEPTION_ILLEGAL_INSTRUCTION:
            crashInfo.type = ExceptionType::IllegalInstruction;
            crashInfo.exceptionMessage = "非法指令";
            break;

        case EXCEPTION_INT_DIVIDE_BY_ZERO:
        case EXCEPTION_FLT_DIVIDE_BY_ZERO:
            crashInfo.type = ExceptionType::DivideByZero;
            crashInfo.exceptionMessage = "除零错误";
            break;

        case EXCEPTION_FLT_INVALID_OPERATION:
        case EXCEPTION_FLT_OVERFLOW:
        case EXCEPTION_FLT_UNDERFLOW:
            crashInfo.type = ExceptionType::FloatingPointError;
            crashInfo.exceptionMessage = "浮点运算错误";
            break;

        case EXCEPTION_BREAKPOINT:
            crashInfo.type = ExceptionType::BreakPoint;
            crashInfo.exceptionMessage = "断点异常";
            break;

        default:
            crashInfo.type = ExceptionType::Unknown;
            crashInfo.exceptionMessage = getExceptionCodeString(record->ExceptionCode);
            break;
    }

    // 获取模块信息
    HMODULE hModule = nullptr;
    if (GetModuleHandleExA(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS,
                          reinterpret_cast<LPCSTR>(record->ExceptionAddress),
                          &hModule)) {
        crashInfo.moduleName = getModuleInfo(hModule);
    }

    // 生成堆栈跟踪
    if (exceptionInfo->ContextRecord) {
        crashInfo.stackTrace = generateStackTrace(exceptionInfo->ContextRecord);
    }

    return crashInfo;
}

QString CrashHandler::getExceptionTypeString(ExceptionType type)
{
    switch (type) {
        case ExceptionType::AccessViolation: return "访问违例";
        case ExceptionType::StackOverflow: return "栈溢出";
        case ExceptionType::IllegalInstruction: return "非法指令";
        case ExceptionType::DivideByZero: return "除零错误";
        case ExceptionType::FloatingPointError: return "浮点错误";
        case ExceptionType::BreakPoint: return "断点异常";
        case ExceptionType::Signal: return "信号异常";
        case ExceptionType::CppException: return "C++异常";
        case ExceptionType::OutOfMemory: return "内存不足";
        default: return "未知异常";
    }
}

QString CrashHandler::getExceptionCodeString(DWORD code)
{
    switch (code) {
        case EXCEPTION_ACCESS_VIOLATION: return "EXCEPTION_ACCESS_VIOLATION";
        case EXCEPTION_ARRAY_BOUNDS_EXCEEDED: return "EXCEPTION_ARRAY_BOUNDS_EXCEEDED";
        case EXCEPTION_BREAKPOINT: return "EXCEPTION_BREAKPOINT";
        case EXCEPTION_DATATYPE_MISALIGNMENT: return "EXCEPTION_DATATYPE_MISALIGNMENT";
        case EXCEPTION_FLT_DENORMAL_OPERAND: return "EXCEPTION_FLT_DENORMAL_OPERAND";
        case EXCEPTION_FLT_DIVIDE_BY_ZERO: return "EXCEPTION_FLT_DIVIDE_BY_ZERO";
        case EXCEPTION_FLT_INEXACT_RESULT: return "EXCEPTION_FLT_INEXACT_RESULT";
        case EXCEPTION_FLT_INVALID_OPERATION: return "EXCEPTION_FLT_INVALID_OPERATION";
        case EXCEPTION_FLT_OVERFLOW: return "EXCEPTION_FLT_OVERFLOW";
        case EXCEPTION_FLT_STACK_CHECK: return "EXCEPTION_FLT_STACK_CHECK";
        case EXCEPTION_FLT_UNDERFLOW: return "EXCEPTION_FLT_UNDERFLOW";
        case EXCEPTION_ILLEGAL_INSTRUCTION: return "EXCEPTION_ILLEGAL_INSTRUCTION";
        case EXCEPTION_IN_PAGE_ERROR: return "EXCEPTION_IN_PAGE_ERROR";
        case EXCEPTION_INT_DIVIDE_BY_ZERO: return "EXCEPTION_INT_DIVIDE_BY_ZERO";
        case EXCEPTION_INT_OVERFLOW: return "EXCEPTION_INT_OVERFLOW";
        case EXCEPTION_INVALID_DISPOSITION: return "EXCEPTION_INVALID_DISPOSITION";
        case EXCEPTION_NONCONTINUABLE_EXCEPTION: return "EXCEPTION_NONCONTINUABLE_EXCEPTION";
        case EXCEPTION_PRIV_INSTRUCTION: return "EXCEPTION_PRIV_INSTRUCTION";
        case EXCEPTION_SINGLE_STEP: return "EXCEPTION_SINGLE_STEP";
        case EXCEPTION_STACK_OVERFLOW: return "EXCEPTION_STACK_OVERFLOW";
        default: return QString("UNKNOWN_EXCEPTION_0x%1").arg(code, 8, 16, QChar('0'));
    }
}

QString CrashHandler::generateDumpFileName()
{
    QDateTime now = QDateTime::currentDateTime();
    QString appName = QCoreApplication::applicationName();
    if (appName.isEmpty()) {
        appName = "Application";
    }

    return QString("%1_crash_%2_%3.dmp")
           .arg(appName)
           .arg(now.toString("yyyyMMdd_hhmmss"))
           .arg(GetCurrentProcessId());
}

QString CrashHandler::getModuleInfo(HMODULE hModule)
{
    char modulePath[MAX_PATH] = {0};
    if (GetModuleFileNameA(hModule, modulePath, MAX_PATH)) {
        QFileInfo fileInfo(QString::fromLocal8Bit(modulePath));
        return fileInfo.fileName();
    }
    return "未知模块";
}

QString CrashHandler::getFunctionName(DWORD64 address)
{
    // 这里可以使用符号解析来获取函数名
    // 为了简化，暂时返回地址
    return QString("0x%1").arg(address, 0, 16);
}

QString CrashHandler::generateStackTrace(CONTEXT* context)
{
    QString stackTrace;

    // 初始化符号处理
    HANDLE hProcess = GetCurrentProcess();
    SymInitialize(hProcess, nullptr, TRUE);

    // 设置符号选项
    SymSetOptions(SYMOPT_UNDNAME | SYMOPT_DEFERRED_LOADS);

    STACKFRAME64 stackFrame = {0};
    DWORD machineType;

#ifdef _M_X64
    machineType = IMAGE_FILE_MACHINE_AMD64;
    stackFrame.AddrPC.Offset = context->Rip;
    stackFrame.AddrPC.Mode = AddrModeFlat;
    stackFrame.AddrFrame.Offset = context->Rbp;
    stackFrame.AddrFrame.Mode = AddrModeFlat;
    stackFrame.AddrStack.Offset = context->Rsp;
    stackFrame.AddrStack.Mode = AddrModeFlat;
#elif _M_IX86
    machineType = IMAGE_FILE_MACHINE_I386;
    stackFrame.AddrPC.Offset = context->Eip;
    stackFrame.AddrPC.Mode = AddrModeFlat;
    stackFrame.AddrFrame.Offset = context->Ebp;
    stackFrame.AddrFrame.Mode = AddrModeFlat;
    stackFrame.AddrStack.Offset = context->Esp;
    stackFrame.AddrStack.Mode = AddrModeFlat;
#else
    return "不支持的架构";
#endif

    int frameCount = 0;
    const int maxFrames = 64;

    while (frameCount < maxFrames) {
        if (!StackWalk64(machineType,
                        hProcess,
                        GetCurrentThread(),
                        &stackFrame,
                        context,
                        nullptr,
                        SymFunctionTableAccess64,
                        SymGetModuleBase64,
                        nullptr)) {
            break;
        }

        if (stackFrame.AddrPC.Offset == 0) {
            break;
        }

        // 获取符号信息
        char symbolBuffer[sizeof(SYMBOL_INFO) + MAX_SYM_NAME * sizeof(TCHAR)];
        SYMBOL_INFO* symbol = reinterpret_cast<SYMBOL_INFO*>(symbolBuffer);
        symbol->SizeOfStruct = sizeof(SYMBOL_INFO);
        symbol->MaxNameLen = MAX_SYM_NAME;

        DWORD64 displacement = 0;
        QString functionName;

        if (SymFromAddr(hProcess, stackFrame.AddrPC.Offset, &displacement, symbol)) {
            functionName = QString::fromLocal8Bit(symbol->Name);
        } else {
            functionName = QString("0x%1").arg(stackFrame.AddrPC.Offset, 0, 16);
        }

        // 获取模块信息
        HMODULE hModule = nullptr;
        QString moduleName = "未知模块";
        if (GetModuleHandleExA(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS,
                              reinterpret_cast<LPCSTR>(stackFrame.AddrPC.Offset),
                              &hModule)) {
            moduleName = getModuleInfo(hModule);
        }

        stackTrace += QString("#%1  0x%2 in %3 (%4)\n")
                     .arg(frameCount, 2, 10, QChar('0'))
                     .arg(stackFrame.AddrPC.Offset, 0, 16)
                     .arg(functionName)
                     .arg(moduleName);

        frameCount++;
    }

    SymCleanup(hProcess);
    return stackTrace;
}
#endif
